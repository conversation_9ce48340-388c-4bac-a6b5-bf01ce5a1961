import NegociacaoComProduto from "../zw/cypress/support/pages/ADM/NZW/NegociacaoComProduto";

beforeEach(() => {
  cy.loginComApi();
});

context('Testa o fluxo de adição de produto na negociação de plano aplicando desconto', () => {
  it('Deve adicionar produto com diversas quantidades na nova tela de negociação', () => {
    NegociacaoComProduto.lancaContrato('1', '10000').then((aluno) => {
      NegociacaoComProduto.validarDesconto(aluno, '575,00', '100,00', '475,00')
    })

    NegociacaoComProduto.lancaContrato('4', '40000').then((aluno) => {
      NegociacaoComProduto.validarDesconto(aluno, '575,00', '400,00', '1.900,00');
    });
  })
});
