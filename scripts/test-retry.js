const argparse = require('argparse');
const axios = require('axios');
const { resetInstancesWithErrors } = require('./cypress-sorry-api');
const ArgumentParser = argparse.ArgumentParser;
const parser = new ArgumentParser();

const projectId = 33594913;

parser.add_argument('-p', '--pipeline-id', {
    help: 'Especifica o código de execução do teste no cypress sorry' });

parser.add_argument('-e', '--test-environment', {
    help: 'Especifica o ambiente de teste que será reexecutado' }
);

parser.add_argument('-t', '--gitlab-token', {
    help: 'Especifica o ambiente de teste que será reexecutado' }
);

async function main(){
    const args = parser.parse_args();
    await resetInstancesWithErrors(args.pipeline_id);
    await retryTestJobs(args.pipeline_id, args.test_environment, args.gitlab_token);
}

async function retryTestJobs(pipelineId, testEnvironment, gitlabToken){
    const testJobName = `deploy-test-${testEnvironment}`;
    const waitJobName = `wait-test-complete-${testEnvironment}`;
    const testJobId = await getJobId(pipelineId, testJobName, gitlabToken);
    const waitJobId = await getJobId(pipelineId, waitJobName, gitlabToken);

    await retryJob(testJobId, testJobName);
    await retryJob(waitJobId, waitJobName);
}

async function retryJob(jobId, jobName, gitlabTken){
    try {
        const response = await axios.post(`https://gitlab.com/api/v4/projects/${projectId}/jobs/${jobId}/retry`,
            {},
            {
                headers: {
                    'PRIVATE-TOKEN': gitlabTken || process.env.GITLAB_TOKEN || process.env.CI_USER_PASS || process.env.CI_JOB_TOKEN 
                }
            });
        if(response.status === 200 || response.status === 201) {
            console.log(`Job ${jobName} ${jobId} reexecutado com sucesso!`);
        }
    }catch (error){
        console.error(error);
        process.exit(1);
    }
};

async function getJobId(pipelineId, jobName, gitlabToken) {
    try {
        const response = await axios.get(`https://gitlab.com/api/v4/projects/${projectId}/pipelines/${pipelineId}/jobs`, {
            headers: {
                    'PRIVATE-TOKEN': gitlabToken || process.env.GITLAB_TOKEN || process.env.CI_USER_PASS || process.env.CI_JOB_TOKEN 
                }
            });
        const job = response.data.find(job => job.name === jobName);
        return job.id;
    }catch (error) {
        if (error.response && error.response.data && error.response.data.message) {
            console.error(`Error: ${error.response.data.message}`);
        } else {
            console.error(error);
        }
        process.exit(1);
    }
};

main();