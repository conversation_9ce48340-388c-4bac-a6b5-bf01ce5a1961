const { exec } = require('child_process');
const inquirer = require('inquirer');

function checkGitLabLogin() {
    exec('docker login registry.gitlab.com -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD', (error, stdout, stderr) => {
        if (error) {
            console.error('Erro ao tentar fazer login no registro do GitLab:', error);
            return;
        }

        if (stdout.includes('Login Succeeded')) {
            console.log('Login no registro docker do GitLab bem-sucedido.');
        } else {
            inquirer
                .prompt([
                    {
                        type: 'confirm',
                        name: 'createToken',
                        message: 'Você precisa criar um GitLab Access Token. Você gostaria de abrir o link para criar um agora?',
                        default: true
                    },
                    {
                        type: 'input',
                        name: 'username',
                        message: 'Digite seu nome de usuário do GitLab:',
                        when: (answers) => answers.createToken === false,
                    },
                    {
                        type: 'password',
                        name: 'password',
                        message: 'Digite personal access token do GitLab:',
                        when: (answers) => answers.createToken === false,
                    },
                ])
                .then((answers) => {
                    exec(`docker login registry.gitlab.com -u ${answers.username} -p ${answers.password}`, (error, stdout, stderr) => {
                        if (error) {
                            console.error('Erro ao tentar fazer login no registro do GitLab:', error);
                            return;
                        }

                        if (stdout.includes('Login Succeeded')) {
                            console.log('Login no registro do GitLab bem-sucedido.');
                        } else {
                            console.error('Falha no login no registro do GitLab. Por favor, tente novamente.');
                        }
                    });
                });
        }
    });
}

checkGitLabLogin();