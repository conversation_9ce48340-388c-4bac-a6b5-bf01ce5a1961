#!/usr/bin/env node

const { execSync } = require('child_process');
const axios = require('axios');
const argparse = require('argparse');
const ArgumentParser = argparse.ArgumentParser;
const parser = new ArgumentParser();

parser.add_argument('-s', '--stack-number', {default: 1, type: 'int'});
parser.add_argument('-t', '--timeout', {default: 0, type: 'int'});
parser.add_argument('-tn', '--timeout-no-search-service', {default: 10000, type: 'int'});
parser.add_argument('-i', '--interval', {default: 1000, type: 'int'});
parser.add_argument('-ng', '--notificate-google-chat', {default: 'false', type: 'string'});
const args = parser.parse_args();

let service_name = `test-${args.stack_number}_cypress`;
let time = 0;
let time_no_search_service = 0;
let timeoutExceded = false;
const notificateGoogleChat = args.notificate_google_chat === 'true';

console.log('⏰ [WAIT TEST] Iniciando monitoramento do teste...');
console.log('📋 [WAIT TEST] Configurações:');
console.log('   - Service name:', service_name);
console.log('   - Stack number:', args.stack_number);
console.log('   - Timeout:', args.timeout, 'ms');
console.log('   - Timeout no search service:', args.timeout_no_search_service, 'ms');
console.log('   - Interval:', args.interval, 'ms');
console.log('   - Notificar Google Chat:', notificateGoogleChat);

async function waitTest() {
    console.log('🔄 [WAIT TEST] Iniciando loop de monitoramento...');

    while (true) {
        try {
            if((args.timeout !== 0 && time >= args.timeout)) {
                timeoutExceded = true;
                const msg = `O timeout do teste foi excedido em ${args.timeout}ms.`;
                console.log(msg);
                const resultTimeout = execSync(`docker service ps ${service_name}`).toString();
                console.error(resultTimeout);
                await sendNotificationToGoogleChat();
                process.exit(1);
            }

            if(time_no_search_service >= args.timeout_no_search_service) {
                timeoutExceded = true;
                const msg = `O timeout de serviço não encontrado foi excedido em ${args.timeout_no_search_service}ms.`;
                console.log(msg);
                await sendNotificationToGoogleChat(msg);
                process.exit(1);
            }

            let tasksStatus = execSync(`docker service ps ${service_name} --format '{{.Name}}: {{.CurrentState}}'`).toString();
            tasksStatus = tasksStatus.split('\n');
            const tasksRunning = tasksStatus.filter(task => task.includes('Running') || task.includes('Starting') || task.includes('Preparing'));
            const tasksFailed = tasksStatus.filter(task => task.includes('Failed') || task.includes('Reject'));
    
            if(tasksRunning.length > 0) {
                console.log(`🔄 [WAIT TEST] Tarefas de teste do service ${service_name} estão executando (${tasksRunning.length} tarefas).`);
                console.log(`⏱️ [WAIT TEST] Tempo decorrido: ${time}ms`);
                if(process.env.DEBUG_DEPLOY === 'true') {
                    const resultRunning = execSync(`docker service ps ${service_name}`).toString();
                    console.info('🐳 [WAIT TEST] Status detalhado do serviço:');
                    console.info(resultRunning);
                }

                await sleep();
                continue;
            }
    
            if(tasksFailed.length > 0){
                const msg = `Alguns testes do service ${service_name} falharam.`;
                console.log(msg);
                const resultFailed = execSync(`docker service ps ${service_name}`).toString();
                console.error(resultFailed);
                await sendNotificationToGoogleChat(msg);
                process.exit(1);
            }
    
            if(tasksFailed.length === 0) {
                const msg = `Todas os testes do service ${service_name} foram executados com sucesso.`;
                console.log(msg);
                const resultSuccess = execSync(`docker service ps ${service_name}`).toString();
                console.info(resultSuccess);
                await sendNotificationToGoogleChat(msg);
                process.exit(0);
            }
        } catch (error) {
            if(timeoutExceded) {
                process.exit(1);
            }

            if(error && error.message && error.message.toLowerCase().includes('no such service')) {
                console.log(`🔍 [WAIT TEST] O Service ${service_name} não foi encontrado. Aguardando... (${time_no_search_service}ms)`);
                time_no_search_service += args.interval;
                await sleep();
                continue;
            }
    
            const msg = `Erro ao aguardar o teste: ${error}`;
            console.error('Erro ao aguardar o teste: ', error);
            await sendNotificationToGoogleChat(msg);
            process.exit(1);
        }
    }
}

async function sleep() {
    time += args.interval;
    return new Promise(resolve => setTimeout(resolve, args.interval));
}

async function sendNotificationToGoogleChat(msg) {
    if(notificateGoogleChat !== 'true'){
        return;
    }
    const webhookUrl = 'https://chat.googleapis.com/v1/spaces/AAAA9CQXzfA/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=m-Gf70kxX-dzNGOtJUwr57V0N5kqEpbrVDCNG9CswYw';
    const payload = {
        text: msg
    };

    const config = {
        headers: {
            'Content-Type': 'application/json; charset=UTF-8'
        }
    };
    try {
        const response = await axios.post(webhookUrl, payload, config);
        console.log('Enviado a notificação para google chat:', response);
    } catch (error) {
        console.error('Erro ao enviar a notificação para google chat:', error.response.data.error);
    }
}

waitTest();