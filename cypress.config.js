const { defineConfig } = require("cypress");

module.exports = defineConfig({
  chromeWebSecurity: false,
  video: false,
  videoUploadOnPasses: false,
  viewportWidth: 1920,
  viewportHeight: 1280,
  e2e: {
    experimentalSessionAndOrigin: true,
    numTestsKeptInMemory: 3,
    retries: {
      runMode: 3,
      openMode: 0
    },
    setupNodeEvents(on, config) {
      on('task', {
        log(message) {
          console.log(message);
          return null;
        }
      });
      
      return config;
    },
    baseUrl: 'http://localhost:3000',
    specPattern: 'cypress/e2e/**/*.{js,jsx,ts,tsx}',
  },
});
