//........................Outros Cadastros ..........................//////////

/// <reference types="cypress" />

describe(' Menu Explorar Cadastros de Produtos',  function () {

    

    beforeEach(() => {
        cy.login('adm')
        cy.abrirFuncLupa('sorteio')
        cy.waitVisible('[id="topbar-modules-toggle"]')
        cy.get('[id="topbar-modules-toggle"]').click()

    })
//.......................Caso de Teste 1........//////////..................  
it('Categoria de Cliente',  () => {
    cy.get('#explorar-module-adm_legado').realHover('mouse')
    cy.waitVisible('[id*="explorar-menu-adm_legado-cadastroCategoriaCliente"]')
    cy.get('[id*="explorar-menu-adm_legado-cadastroCategoriaCliente"]').eq(0).click()
    cy.contains('Categoria de Clientes')
})
//.......................Caso de Teste 2........//////////..................  
it('Cidade',  () => {
    cy.get('#explorar-module-adm_legado').realHover('mouse')
    cy.waitVisible('[id*="explorar-menu-adm_legado-cadastroCidade"]')
    cy.get('[id*="explorar-menu-adm_legado-cadastroCidade"]').eq(0).click()
    cy.contains('Cidade')
})
//.......................Caso de Teste 3........//////////..................  
it('Classificação',  () => {
    cy.get('#explorar-module-adm_legado').realHover('mouse')
    cy.waitVisible('[id*="explorar-menu-adm_legado-cadastroCidade"]')
    cy.get('[id*="explorar-menu-adm_legado-cadastroClassificacao"]').eq(0).click()
    cy.contains('Classificação')
})
//.......................Caso de Teste 4........//////////..................  
it(' Colaborador ',  () => {
    cy.get('#explorar-module-adm_legado').realHover('mouse')
    cy.waitVisible('[id*="explorar-menu-adm_legado-cadastroColaborador"]')
    cy.window().then((win) => { //abrir o pop na tela principal
    cy.get('[id*="explorar-menu-adm_legado-cadastroColaborador"]').eq(0).click()
    cy.stub(win, 'open', url => {
        win.location.href = url
      }).as("popup") 
   })
    //cy.contains('Colaborador')   
})
//.......................Caso de Teste 5........//////////..................  
it(' Departamento  ',  () => {
    cy.get('#explorar-module-adm_legado').realHover('mouse')
    cy.waitVisible('[id*="explorar-menu-adm_legado-cadastroDepartamento"]')
    cy.get('[id*="explorar-menu-adm_legado-cadastroDepartamento"]').eq(0).click()
    cy.contains('Departamento')   

})
//.......................Caso de Teste 6........//////////..................  
it('Empresa ',  () => {
    cy.get('#explorar-module-adm_legado').realHover('mouse')
    cy.waitVisible('[id*="explorar-menu-adm_legado-cadastroEmpresa"]')
    cy.window().then((win) => { //abrir o pop na tela principal
    cy.get('[id*="explorar-menu-adm_legado-cadastroEmpresa"]').eq(0).click()
    cy.stub(win, 'open', url => {
        win.location.href = url
      }).as("popup") 
   })
    cy.contains('Empresa')    
})
//.......................Caso de Teste 7........//////////..................  
it(' Grau de Instrução  ',  () => {
    cy.get('#explorar-module-adm_legado').realHover('mouse')
    cy.waitVisible('[id*="explorar-menu-adm_legado-cadastroGrauInstrucao"]')
    cy.get('[id*="explorar-menu-adm_legado-cadastroGrauInstrucao"]').eq(0).click()
    cy.contains('Grau de Instrução ')   
   

         
})

})