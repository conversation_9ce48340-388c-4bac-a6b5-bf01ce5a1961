import LoginPage from "@pages/LOGIN/LoginPage";
import Usuario from "@models/Usuario";
import EnvUtils from "@utils/EnvUtils";
import {NOVO_ADMINISTRATIVO} from "@models/ModulosEnum";
import VendaAvulsaPage from "support/pages/angular/adm/VendaAvulsaPage";
import MenuSuperiorPage from "support/pages/angular/adm/MenuSuperiorPage";
import {ModulosEnum} from "../../support/pages/angular/modulos-enum";
import AdmMsClientesApi from "@services/AdmMs/AdmMsClientesApi"
import {FANTASIA} from "@models/EmpresasEnum"
import DescontoPage from "support/pages/angular/adm/DescontoPage";
import NovoCaixaAbertoPage from "support/pages/angular/novo-caixa-aberto/NovoCaixaAbertoPage";

describe('Venda Avulsa', () => {

    beforeEach(() => {
        LoginPage.logarSemSessao(EnvUtils.usuarioEnzo(), NOVO_ADMINISTRATIVO, 1);
    });

    it('SP-2030 | Realizar uma Venda Avulsa Com Desconto Padrão', () => {
        MenuSuperiorPage.buscarFuncionalidadesNaLupa('Desconto', {matchMode: 'exact'})
        DescontoPage.cadastrarDescontoPadraoVendaAvulsa()
        AdmMsClientesApi.incluirCliente(FANTASIA).then(novoAluno => {
            MenuSuperiorPage.menuExplorar('Venda Avulsa', ModulosEnum.ADM_LEGADO);
            VendaAvulsaPage.realizarVendaAvulsaDescontoExtra(novoAluno);
            NovoCaixaAbertoPage.receberParcelasDinheiro()
        });
    });
});
