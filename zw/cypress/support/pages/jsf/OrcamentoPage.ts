import * as faker from 'faker-br'

class OrcamentoPage {
    elements = {
        MENU_CRIAR_ORCAMENTO: '#sidebar-item-orcamento > .pacto-item-menu > .pacto-item-menu-content',
        SELECT_CONSULTOR: '#form\\:consultor',
        PROSPECTO: '#form\\:nomeCliente',
        PROSPECTO_SELECIONADO: '.rich-sb-cell-padding > .texto-font',
        TIPO_PROSPECTO: '#form\\:paraquem',
        INPUT_TIPO_PROSPECTO: '#form\\:nomeProspecto',
        INPUT_IDADE_PROSPECTO: '#form\\:idade',
        SELECT_ORCAMENTO: '#form\\:modeloorcamento',
        PERIODO: '#form\\:periodo',
        SITUACAO: '#form\\:situacao',
        TIPO_TURMA: '#form\\:tipoturma',
        CONCLUIR: '#form\\:salvar'
    }

    capturaPopUp() {
        cy.window().then((win) => {
            cy.stub(win, "open", (url) => {
                win.location.href = Cypress.config().baseUrl + "/faces/" + url;
            }).as("popup");
        })
    }

    criarOrcamento(): void {
        cy.get(this.elements.MENU_CRIAR_ORCAMENTO).should('be.visible').click()
        cy.get(this.elements.SELECT_CONSULTOR).select('PACTO - MÉTODO DE GESTÃO')
        cy.get(this.elements.PROSPECTO).type('AA_PRIMEIRO CLIENTE')
        cy.get(this.elements.PROSPECTO_SELECIONADO).type('AA_PRIMEIRO CLIENTE')
        cy.get(this.elements.TIPO_PROSPECTO).select('Outro').wait(500)
        cy.get(this.elements.INPUT_TIPO_PROSPECTO).should('be.visible').type(faker.name.findName(), {force: true})
        cy.get(this.elements.INPUT_IDADE_PROSPECTO).should('be.visible').clear().type('32')
        cy.get(this.elements.SELECT_ORCAMENTO).select('1')
        cy.get(this.elements.PERIODO).select('Todos os períodos')
        cy.get(this.elements.SITUACAO).select('Andamento')
        cy.get(this.elements.TIPO_TURMA).select('Todas')
        cy.get(this.elements.CONCLUIR).should('be.visible').click()
        cy.contains('Dados Gravados com Sucesso').should('be.visible')
    }


}

export default new OrcamentoPage();
