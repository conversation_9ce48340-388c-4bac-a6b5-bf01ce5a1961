# Configuração do Cypress - teste-auto

## Estrutura do Projeto

O Cypress foi configurado no diretório raiz do projeto com a seguinte estrutura:

```
teste-auto/
├── cypress.config.js          # Configuração principal do Cypress
├── cypress/
│   ├── e2e/                   # Testes end-to-end
│   │   └── exemplo.cy.js      # Teste de exemplo
│   ├── fixtures/              # Dados de teste (JSON, etc.)
│   └── support/               # Comandos customizados e configurações
│       ├── commands.js        # Comandos customizados
│       └── e2e.js            # Configurações globais
└── zw/                        # Configuração existente do Cypress
```

## Como Executar os Testes

### Pré-requisitos
1. **Node.js**: É necessário ter o Node.js instalado no sistema
2. **Cypress**: Instalar o Cypress como dependência do projeto

### Instalação do Node.js
Se o Node.js não estiver instalado, você pode:
1. Baixar do site oficial: https://nodejs.org/
2. Ou usar o winget: `winget install OpenJS.NodeJS`

### Instalação do Cypress
Após ter o Node.js instalado, execute:
```bash
npm install cypress --save-dev
```

### Executar os Testes
```bash
# Abrir a interface gráfica do Cypress
npx cypress open

# Executar testes em modo headless
npx cypress run

# Executar um teste específico
npx cypress run --spec "cypress/e2e/exemplo.cy.js"
```

## Configuração Atual

O arquivo `cypress.config.js` está configurado com:
- **baseUrl**: http://localhost:3000 (ajuste conforme necessário)
- **viewportWidth**: 1920px
- **viewportHeight**: 1280px
- **video**: desabilitado
- **chromeWebSecurity**: desabilitado
- **retries**: 3 tentativas em modo de execução

## Próximos Passos

1. **Instalar Node.js** se ainda não estiver instalado
2. **Instalar Cypress** usando npm
3. **Ajustar a baseUrl** no arquivo `cypress.config.js` para apontar para sua aplicação
4. **Criar seus testes** na pasta `cypress/e2e/`
5. **Executar os testes** usando os comandos acima

## Exemplo de Uso

O arquivo `cypress/e2e/exemplo.cy.js` contém um teste básico que você pode usar como referência para criar seus próprios testes.

## Configuração Avançada

Para configurações mais avançadas, consulte a pasta `zw/` que contém uma configuração mais complexa com:
- Integração com ambientes
- Configurações de usuários
- URLs de descoberta de serviços
- Integração com Cypress Cloud
